import React, { useEffect } from 'react';
import { Route, Switch } from 'react-router-dom';

import { hot } from 'react-hot-loader/root';

import ListBeneficiaries from 'containers/Beneficiary/ListBeneficiaries';
import BeneficiaryProfile from 'containers/Beneficiary/BeneficiaryProfile';
import BeneficiaryAdd from 'containers/Beneficiary/BeneficiaryAdd';

import Donor from 'containers/Donor/Donors';
import DonorFiche from 'containers/Donor/DonorProfile';
import DonorPhysicalAdd from 'containers/Donor/DonorPhysicalAdd';
import DonorMoralAdd from 'containers/Donor/DonorMoralAdd';

import FicheDonation from 'containers/Donation/FicheDonation';
import AddDonation from 'containers/Donation/AddDonation';
import Donations from 'containers/Donation/Donations';

import ListFamilies from 'containers/Family/ListFamilies';
import FamilyAdd from 'containers/Family/FamilyAdd';
import FamilyProfile from 'containers/Family/FamilyProfile';

import AddTakenInCharge from 'containers/TakenInCharge/AddTakenInCharge';
import TakenInCharges from 'containers/TakenInCharge/TakenInCharges';
import TakenInChargeProfile from 'containers/TakenInCharge/TakenInChargeProfile';
import AdUser from 'containers/AdUser';
import Profiles from 'containers/Profile/Profiles';
import AddProfile from 'containers/Profile/AddProfile';
import {
  isAuthorized,
  isAuthorizedCombinations,
  isAuthorizedOld,
} from 'utils/AccessControl';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import ErrorModal from 'components/Common/ErrorModal/ErrorModal';
import saga from './saga';
import reducer from './reducer';
import Parametre from '../Parametre';

import SuivieDocumentsRenouvler from 'components/SuivieDocumentsRenouvler'
import SuivieTakenInCharge from 'components/SuivieTakenInCharge';
import Zones from 'containers/Zone/Zones';
import EpsList from 'containers/Eps/EpsList';
import AddZone from 'containers/Zone/AddZone';
import FicheZone from 'containers/Zone/FicheZone';
import Assistants from 'containers/Assistant/Assistants';
import AddAssistant from 'containers/Assistant/AddAssistant';
import DonorAnonymeAdd from 'containers/Donor/DonorAnonymeAdd';
import AddCaisse from 'containers/Caisse/AddCaisse';
import Caisses from 'containers/Caisse/Caisses';
import CaisseProfile from 'containers/Caisse/FicheCaisse';
import BeneficiariesAdHoc from 'containers/BeneficiaryAdHoc/ListBeneficiariesAdHoc';
import BeneficiaryAdHocPersonAdd from 'containers/BeneficiaryAdHoc/BeneficiaryAdHocPersonAdd';
import BeneficiaryAdHocGroupeAdd from 'containers/BeneficiaryAdHoc/BeneficiaryAdHocGroupeAdd';
import BeneficiariesAdHocFiche from 'containers/BeneficiaryAdHoc/BeneficiariesAdHocProfile';
import RapportForm from 'containers/Common/SubComponents/RapportForm';
import TranslationContainer from 'containers/TranslationContainer';
import EspaceAssistant from 'containers/EspaceAssistant/FicheAssistant';
import SuivieAssistantsDocumentsRenouvler from 'components/espaceAssistant/documentsRenouvler';
import GlobalStyle from '../../global-styles';
import ChangeLogPage from '../../components/Common/Footer/ChangeLogPage';
import LoginPage from '../LoginPage';
import HomePage from '../HomePage';
import NotFound from '../NotFoundPage';
import PisteAudit from '../PistAudit';
import MyAppBar from '../../components/Common/AppBar';
import Actions from '../../components/Actions';
import { fetchConnectedUserRequest } from './actions';
import { makeSelectConnectedUser, makeSelectError } from './selectors';

import Candidat from '../Beneficiary/ListCandidates';
import BeneficiariesArchived from '../Beneficiary/ListBeneficiariesArchived';
import AideComplementaire from '../AideComplementaire/ListAideComplementaire';
import AddAideComplementaire from '../AideComplementaire/AddAideComplementaire';
import FicheAideComplementaire from '../AideComplementaire/FicheAideComplementaire';
import AddService from '../Service/AddService';
import Service from '../Service/ListService';
import FicheService from '../Service/FicheService';
import CandidatesKafalat from '../Beneficiary/ListCandidatesKafalat';
import SuivieRapportsKafalat from 'components/SuivieRapportsKafalat';
import AddEps from 'containers/Eps/AddEps';
import ServiceCollectEpsAdd from 'containers/ServiceCollectEps/AddServiceCollectEps';

import TagList from '../tag'
import Reclamation from 'containers/Reclamation/ListReclamation';

import ficheEps from 'containers/Eps/FicheEps';
import ServiceCollectEpsList from 'containers/ServiceCollectEps/ListServiceCollectEps';
import { ficheServiceCollectEpsLoaded } from 'containers/ServiceCollectEps/fichServiceCollectEps/actions';
import ServiceCollectEpsFiche from 'containers/ServiceCollectEps/fichServiceCollectEps';
import RapportsAssistant from '../EspaceAssistant/RapportsAssistant';
import DashboardGeneral from '../../components/Dashboard-General';
import DashboardDonors from '../../components/Dashboard-Donors';
import DashboardBeneficiaries from '../../components/Dashboard-Beneficiaries';
import DashboardDonations from '../../components/Dashboard-Donations';
import DashboardKafalat from '../../components/Dashboard-Kafalat';
import DashboardAide from '../../components/Dashboard-Aide';
import DashboardEPS from '../../components/Dashboard-EPS';
import UsersForMobile from 'containers/UsersForMobile';
import AddUser from 'containers/UsersForMobile/AddUser';
import EditUser from 'containers/UsersForMobile/EditUser';
import ViewUser from 'containers/UsersForMobile/ViewUser';
import CommunCaisseList from '../CommunCaisse';
import AddOrUpdateCaisse from '../CommunCaisse/AddOrUpdateCaisse';
const key = 'app';

const omdbSelector = createStructuredSelector({
  connectedUser: makeSelectConnectedUser,
  connectUserError: makeSelectError,
});

function App() {
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const dispatch = useDispatch();
  const { connectUserError, connectedUser } = useSelector(omdbSelector);

  useEffect(() => {
    if (!connectedUser) {
      dispatch(fetchConnectedUserRequest());
    }
  }, [dispatch]);

  return (
    <>
      {connectUserError && <ErrorModal error={connectUserError} />}

      {connectedUser && (
        <div>
          <TranslationContainer />
          <MyAppBar />
          <div className="temp-fix">

          <Switch>
            {/* Dashboard Routes */}
            <Route path="/dashboard-general" component={DashboardGeneral} />
            <Route path="/dashboard-donors" component={DashboardDonors} />
            <Route path="/dashboard-beneficiaries" component={DashboardBeneficiaries} />
            <Route path="/dashboard-donations" component={DashboardDonations} />
            <Route path="/dashboard-kafalat" component={DashboardKafalat} />
            <Route path="/dashboard-aide" component={DashboardAide} />
            <Route path="/dashboard-eps" component={DashboardEPS} />

            {isAuthorizedOld(connectedUser, 'USER', 'VIEW') && [
              <Route key="adUser" path="/utilisateur" component={AdUser} />,
            ]}
            {isAuthorizedOld(connectedUser, 'USER', 'UPDATE') && [
              <Route
                key="addProfile"
                path="/Profiles/addProfile"
                component={AddProfile}
              />,
              <Route
                key="ficheZone"
                path="/zones/fiche/:id"
                component={FicheZone}
              />,
              <Route
                key="editZone"
                path="/Zones/editZone/:idZone"
                component={AddZone}
              />,
              <Route key="addZone" path="/Zones/addZone" component={AddZone} />,
              <Route
                key="editEps"
                path="/Eps/editEps/:idEps"
                component={AddEps}
              />,
              <Route key="addEps" path="/Eps/addEps" component={AddEps} />,
              <Route key="addServiceCollectEps" path="/ServiceCollectEps/add" component={ServiceCollectEpsAdd} />,
              <Route key="ServiceCollectEps" path="/ServiceCollectEps" exact component={ServiceCollectEpsList} />,
              <Route key="addServiceCollectEps" path="/ServiceCollectEps/edit/:idEps" component={ServiceCollectEpsAdd} />,
              <Route key="ficheServiceCollectEps" path="/ServiceCollectEps/fiche/:id/info" component={ServiceCollectEpsFiche} />,
              <Route key="Zones" path="/Zones" component={Zones} />,
              //Eps List Route
              <Route
                key="addAssistant"
                path="/Assistants/addAssistant"
                component={AddAssistant}
              />,
              <Route
                key="editAssistant"
                path="/Assistants/editAssistant/:idAssistant"
                component={AddAssistant}
              />,
              <Route
                key="consultAssistant"
                path="/Assistants/consultAssistant/:idAssistant/:isRead"
                component={AddAssistant}
              />,

                <Route
                  key="Assistants"
                  path="/Assistants"
                  component={Assistants}
                />,

                <Route
                  key="editProfile"
                  path="/Profiles/editProfile/:idProfile"
                  component={AddProfile}
                />,
                <Route key="Profiles" path="/Profiles" component={Profiles} />,
              ]}
              {isAuthorizedOld(connectedUser, 'DONATION', 'CREATE') && [
                <Route
                  key="addDonation"
                  path="/donations/add"
                  component={AddDonation}
                />,
              ]}
              {isAuthorizedOld(connectedUser, 'DONATION', 'UPDATE') && [
                <Route
                  key="editDonation"
                  path="/donations/edit/:idDonation"
                  component={AddDonation}
                />,
              ]}
              {isAuthorizedOld(connectedUser, 'DONATION', 'VIEW') && [
                <Route
                  key="ficheDonation"
                  path="/donations/fiche/:id"
                  component={FicheDonation}
                />,
                <Route
                  key="donations"
                  path="/donations"
                  component={Donations}
                />,
              ]}
              {isAuthorizedOld(connectedUser, 'DONOR', 'CREATE') && [
                <Route
                  key="addPhysicalDonor"
                  exact
                  path="/donors/add/physique"
                  component={DonorPhysicalAdd}
                />,
                <Route
                  key="addMoralDonor"
                  exact
                  path="/donors/add/moral"
                  component={DonorMoralAdd}
                />,
                <Route
                  key="addAnonymousDonor"
                  exact
                  path="/donors/add/anonyme"
                  component={DonorAnonymeAdd}
                />,
              ]}
              {isAuthorizedOld(connectedUser, 'DONOR', 'UPDATE') && [
                <Route
                  key="editPhysicalDonor"
                  path="/donors/edit/physique/:id"
                  component={DonorPhysicalAdd}
                />,
                <Route
                  key="editMoralDonor"
                  path="/donors/edit/moral/:id"
                  component={DonorMoralAdd}
                />,
                <Route
                  key="editMoralDonor"
                  path="/donors/edit/anonyme/:id"
                  component={DonorAnonymeAdd}
                />,
              ]}
              {isAuthorizedOld(connectedUser, 'DONOR', 'VIEW') && [
                <Route key="donors" exact path="/donors" component={Donor} />,
                <Route
                  key="donorFiche"
                  path="/donors/fiche/:id"
                  component={DonorFiche}
                />,
              ]}
              {isAuthorized(connectedUser, 'GERER_CANDIDATS', 'WRITE') && [
                <Route
                  key="addBeneficiary"
                  exact
                  path="/beneficiaries/add"
                  component={BeneficiaryAdd}
                />,
              ]}
              {isAuthorizedCombinations(connectedUser, [
                { feature: 'GERER_BENEFICIAIRES_KAFALAT', privilege: 'WRITE' },
                { feature: 'GERER_CANDIDATS', privilege: 'WRITE' },
              ]) && [
                <Route
                  key="editBeneficiary"
                  exact
                  path="/beneficiaries/edit/:id"
                  component={BeneficiaryAdd}
                />,
                <Route
                  key="addRapport"
                  exact
                  path="/beneficiaries/add/rapport"
                  component={RapportForm}
                />,
                <Route
                  key="updateRapport"
                  exact
                  path="/beneficiaries/update/rapport"
                  component={RapportForm}
                />,
                <Route
                  key="editRapport"
                  path="/beneficiaries/edit/rapport/:id"
                  component={RapportForm}
                />,
              ]}
              {isAuthorized(
                connectedUser,
                'GERER_BENEFICIAIRES_KAFALAT',
                'READ',
              ) && [
                <Route
                  key="beneficiaries"
                  exact
                  path="/beneficiaries"
                  component={ListBeneficiaries}
                />,
              ]}
              {isAuthorized(
              connectedUser,
              'GERER_BENEFICIAIRES_KAFALAT',
              'READ',
            ) && [
              <Route
                key="candidatesKafalat"
                exact
                path="/candidatesKafalat"
                component={CandidatesKafalat}
              />,
            ]}
            {isAuthorizedCombinations(connectedUser, [
              { feature: 'GERER_BENEFICIAIRES_KAFALAT', privilege: 'READ' },
              { feature: 'GERER_CANDIDATS', privilege: 'READ' },
              { feature: 'GERER_BENEFICIAIRES_ARCHIVES', privilege: 'READ' },
            ]) && [
              <Route
                key="beneficiaryProfile"
                path="/beneficiaries/fiche/:id"
                component={BeneficiaryProfile}
              />,
            ]}
            {isAuthorized(connectedUser, 'GERER_CANDIDATS', 'READ') && [
              <Route
                key="candidats"
                exact
                path="/candidats"
                component={Candidat}
              />,
            ]}
            {isAuthorized(
              connectedUser,
              'GERER_BENEFICIAIRES_ARCHIVES',
              'READ',
            ) && [
              <Route
                key="beneficiariesArchived"
                exact
                path="/archived-beneficiaries"
                component={BeneficiariesArchived}
              />,
            ]}
            {isAuthorized(
              connectedUser,
              'GERER_BENEFICIAIRES_AD_HOC',
              'READ',
            ) && [
              <Route
                key="beneficiariesAdHoc"
                exact
                path="/beneficiaries-ad-hoc"
                component={BeneficiariesAdHoc}
              />,
              <Route
                key="beneficiaryAdHocFiche"
                path="/beneficiaries/ad-hoc/fiche/:id"
                component={BeneficiariesAdHocFiche}
              />,
            ]}
            {isAuthorized(
              connectedUser,
              'GERER_BENEFICIAIRES_AD_HOC',
              'WRITE',
            ) && [
              <Route
                key="addbeneficiaryAdHocPersonne"
                exact
                path="/beneficiaries/add/ad-hoc/personne"
                component={BeneficiaryAdHocPersonAdd}
              />,
              <Route
                key="editbeneficiaryAdHocPersonne"
                path="/beneficiaries/edit/ad-hoc/personne/:id"
                component={BeneficiaryAdHocPersonAdd}
              />,
              <Route
                key="addbeneficiaryAdHocGroup"
                exact
                path="/beneficiaries/add/ad-hoc/groupe"
                component={BeneficiaryAdHocGroupeAdd}
              />,
              <Route
                key="editbeneficiaryAdHocGroup"
                exact
                path="/beneficiaries/edit/ad-hoc/groupe/:id"
                component={BeneficiaryAdHocGroupeAdd}
              />,
              <Route
                key="beneficiaryAdHocFiche"
                path="/beneficiaries/ad-hoc/fiche/:id"
                component={BeneficiariesAdHocFiche}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'FAMILLE', 'CREATE') && [
              <Route
                key="addFamily"
                exact
                path="/families/add"
                component={FamilyAdd}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'FAMILLE', 'UPDATE') && [
              <Route
                key="editFamily"
                path="/families/edit/:id"
                component={FamilyAdd}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'FAMILLE', 'VIEW') && [
              <Route
                key="families"
                exact
                path="/families"
                component={ListFamilies}
              />,
              <Route
                key="familyProfile"
                path="/families/fiche/:id"
                component={FamilyProfile}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'TAKEINCHARGE', 'CREATE') && [
              <Route
                key="addTakenInCharge"
                exact
                path="/takenInCharges/add"
                component={AddTakenInCharge}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'TAKEINCHARGE', 'UPDATE') && [
              <Route
                key="editTakenInCharge"
                path="/takenInCharges/edit/:idTakenInCharge"
                component={() => <AddTakenInCharge isEditable />}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'TAKEINCHARGE', 'VIEW') && [
              <Route
                key="takenInCharges"
                exact
                path="/takenInCharges"
                component={TakenInCharges}
              />,
              <Route
                key="takenInChargeProfile"
                path="/takenInCharges/fiche/:id"
                component={TakenInChargeProfile}
              />,
            ]}
            <Route
              key="addCaisse"
              exact
              path="/caisses/add"
              component={AddCaisse}
            />
            <Route
              key="editCaisse"
              path="/caisses/edit/:idCaisse"
              component={() => <AddCaisse isEditable />}
            />
            ,
            <Route key="Caisses" exact path="/Caisses" component={Caisses} />
            <Route
              key="caisseProfile"
              path="/caisses/fiche/:id"
              component={CaisseProfile}
            />
            <Route
              key="AideComplementaire"
              exact
              path="/aide-complementaire"
              component={AideComplementaire}
            />
            <Route
              key="addAideComplementaire"
              exact
              path="/aide-complementaire/add"
              component={AddAideComplementaire}
            />
            <Route
              key="editAideComplementaire"
              path="/aide-complementaire/edit/:idAideComplementaire"
              component={AddAideComplementaire}
            />
            <Route
              key="ficheAideComplementaire"
              path="/aide-complementaire/fiche/:id"
              component={FicheAideComplementaire}
            />
            {isAuthorizedOld(connectedUser, 'USER', 'CREATE') && [
              <Route
                key="addService"
                exact
                path="/services/add"
                component={AddService}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'USER', 'VIEW') && [
              <Route
                key="services"
                exact
                path="/services"
                component={Service}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'USER', 'VIEW') && [
              <Route
                key="ficheServices"
                path="/services/fiche/:id"
                component={FicheService}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'USER', 'UPDATE') && [
              <Route
                key="editService"
                path="/services/edit/:idService"
                component={AddService}
              />,
            ]}
            , , ,
            {isAuthorizedOld(connectedUser, 'USER', 'CREATE') && [
              <Route
                key="addCaisse"
                exact
                path="/caisses/add"
                component={AddCaisse}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'USER', 'UPDATE') && [
              <Route
                key="editCaisse"
                path="/caisses/edit/:idCaisse"
                component={() => <AddCaisse isEditable />}
              />,
            ]}
            {isAuthorizedOld(connectedUser, 'USER', 'VIEW') && [
              <Route key="Caisses" exact path="/Caisses" component={Caisses} />,
              <Route
                key="caisseProfile"
                path="/caisses/fiche/:id"
                component={CaisseProfile}
              />,
            ]}
            <Route key="parametre" path="/parametre" component={Parametre} />,
            <Route key="audits" path="/audit" component={PisteAudit} />,
            <Route key="actions" path="/actions" component={Actions} />,
            <Route key="reclamations" path="/reclamations" component={Reclamation} />,
            <Route
              key="SuiviepriseEncharge"
              path="/SuiviepriseEncharge"
              component={SuivieTakenInCharge}
            />
            <Route
              key="SuivieRapportsKafalat"
              path="/SuivieRapportsKafalat"
              component={SuivieRapportsKafalat}
            />

            <Route
              key="SuivieDocumentsRenouvler"
              path="/SuivieDocumentsRenouvler"
              component={SuivieDocumentsRenouvler}
            />
            ,{/* General routes accessible to all users */}
            <Route exact path="/login" component={LoginPage} />
            <Route exact path="/" component={() => {
              window.location.pathname = "/dashboard-general";
              return null;
            }} />
            <Route exact path="/dashboard" component={DashboardGeneral} />
            <Route exact path="/changelog" component={ChangeLogPage} />
            {/* Espace Assistant */}
            <Route
              key="espaceAssistant"
              path="/espace-assistant/fiche/:id"
              component={EspaceAssistant}
            />
            <Route
              key="espaceAssistantRapports"
              path="/espace-assistant/rapports/:id"
              component={RapportsAssistant}
            />
            <Route
              key="espaceAssistantDocuments"
              path="/espace-assistant/documents/:id"
              component={SuivieAssistantsDocumentsRenouvler}
            />


<Route key="Tags" path="/taglist" component={TagList} />

{/* Mobile Users Routes */}
<Route key="mobileUsers" exact path="/mobileUsers" component={UsersForMobile} />
<Route key="addMobileUser" exact path="/mobileUsers/add" component={AddUser} />
<Route key="editMobileUser" path="/mobileUsers/edit/:id" component={EditUser} />
<Route key="viewMobileUser" path="/mobileUsers/view/:id" component={ViewUser} />

{/*
Eps components */}

<Route key="ficheEps" path="/eps/fiche/:id" component={ficheEps} />
<Route key="Eps" path="/eps" component={EpsList} />,

<Route key="CommunCaisseAdd" exact path="/commun-caisse/add" component={AddOrUpdateCaisse} />
<Route key="CommunCaisseEdit" path="/commun-caisse/edit/:id" component={AddOrUpdateCaisse} />
<Route key="CommunCaisse" exact path="/commun-caisse" component={CommunCaisseList} />

            {/* 404 Not Found Page */}
            <Route component={NotFound} />
          </Switch>
          </div>
          <GlobalStyle />
        </div>
      )}
    </>
  );
}

export default hot(App);
