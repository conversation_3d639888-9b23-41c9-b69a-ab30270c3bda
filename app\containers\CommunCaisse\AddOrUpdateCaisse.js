import React, { useState } from 'react';
import CommunCaisseForm from './CommunCaisseForm';
import { useHistory, useLocation } from 'react-router-dom';
import { Alert } from 'react-bootstrap';

export default function AddOrUpdateCaisse({ initialValues = null, isUpdate = false }) {
    const history = useHistory();
    const location = useLocation();
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');

    // If initialValues are not provided, use defaults for add
    const defaultValues = {
        donor: '',
        amount: '',
        date_entre: '',
        date_sortie: '',
        category: '',
        service: '',
        status: '',
    };
    const formInitialValues = initialValues || defaultValues;

    const handleSubmit = () => {
        setShowAlert(true);
        setAlertMessage(isUpdate ? 'Caisse modifiée avec succès !' : 'Caisse ajoutée avec succès !');
        setTimeout(() => {
            setShowAlert(false);
            history.push('/commun-caisse'); // Redirect to list page after submit
        }, 2000);
    };

    return (
        <div style={{ maxWidth: 800, margin: '0 auto', padding: '2rem 1rem' }}>
            <h2 style={{ marginBottom: '2rem' }}>{isUpdate ? 'Modifier une caisse' : 'Ajouter une caisse'}</h2>
            {showAlert && (
                <Alert variant="success" onClose={() => setShowAlert(false)} dismissible>{alertMessage}</Alert>
            )}
            <CommunCaisseForm
                initialValues={formInitialValues}
                onSubmit={handleSubmit}
                isUpdate={isUpdate}
            />
        </div>
    );
} 