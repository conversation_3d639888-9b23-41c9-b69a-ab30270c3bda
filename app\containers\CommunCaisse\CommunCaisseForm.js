import React, { useEffect, useState } from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Grid } from '@mui/material';
import { Alert } from 'react-bootstrap';
import ServiceCategories from 'containers/Common/SubComponents/ServiceCategories';
import { addCaisseRequest, updateCaisseRequest } from './actions';
import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import { CustomSelect } from 'containers/Common/CustomInputs/CustomSelect';
import * as Yup from 'yup';
import { loadActiveServicesByCategory } from 'containers/Service/ListService/actions';
import { makeSelectActiveServices } from 'containers/Service/ListService/selectors';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import serviceReducer from 'containers/Service/ListService/reducer';
import serviceListSaga from 'containers/Service/ListService/saga';
import { createStructuredSelector } from 'reselect';
import ChooseDonor from 'containers/Common/SubComponents/ChooseDonor';
import { makeSelectLoading, makeSelectError, makeSelectSuccess } from './selectors';

const key = 'serviceList';

const validationSchema = Yup.object({
    donor: Yup.object().required('Le donateur est obligatoire'),
    amount: Yup.number().required('Le montant est obligatoire'),
    dateEmprunt: Yup.date().required('La date d\'emprunt est obligatoire'),
    category: Yup.string().required('La catégorie est obligatoire'),
    service: Yup.string().required('Le service est obligatoire'),
});

const stateSelector = createStructuredSelector({
    activeServices: makeSelectActiveServices,
    loading: makeSelectLoading,
    error: makeSelectError,
    success: makeSelectSuccess,
});

export default function CommunCaisseForm({ initialValues, onSubmit, isUpdate }) {
    useInjectReducer({ key, reducer: serviceReducer });
    useInjectSaga({ key, saga: serviceListSaga });
    const dispatch = useDispatch();
    const { activeServices, loading, error, success } = useSelector(stateSelector);
    const [selectedCategory, setSelectedCategory] = useState(initialValues.category || '');

    useEffect(() => {
        if (selectedCategory) {
            dispatch(loadActiveServicesByCategory(selectedCategory));
        }
    }, [selectedCategory, dispatch]);

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            enableReinitialize
            onSubmit={values => {
                if (isUpdate) {
                    dispatch(updateCaisseRequest(values));
                } else {
                    dispatch(addCaisseRequest(values));
                }
                if (onSubmit) onSubmit();
            }}
        >
            {formikProps => (
                <div style={{ background: '#fff', padding: '2rem', borderRadius: '12px' }}>
                    {error && (
                        <Alert variant="danger" style={{ marginBottom: '1rem' }}>
                            {error.response?.data?.detailedMessage || error.message || 'Une erreur est survenue'}
                        </Alert>
                    )}
                    <Form onSubmit={formikProps.handleSubmit}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12}>
                                <div style={{ width: '50%', margin: '0 auto' }}>
                                    <ChooseDonor props={formikProps} />
                                </div>
                            </Grid>

                            <Grid item xs={12} sm={6}>
                                <CustomTextInput name="amount" label="Montant *" type="number" formProps={formikProps} />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <CustomTextInput name="dateEmprunt" label="Date Emprunt *" type="date" formProps={formikProps} />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <ServiceCategories
                                    name="category"
                                    label="Catégorie *"
                                    value={formikProps.values.category || ''}
                                    isRequired={true}
                                    onSelectCanal={cat => {
                                        formikProps.setFieldValue('category', cat.id);
                                        setSelectedCategory(cat.id);
                                        formikProps.setFieldValue('service', ''); // reset service when category changes
                                    }}
                                    formProps={formikProps}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <CustomSelect
                                    name="service"
                                    label="Service *"
                                    isRequired={true}
                                    formProps={formikProps}
                                >
                                    <option value="">-- Sélectionner un service --</option>
                                    {activeServices && Array.isArray(activeServices) && activeServices.map(service => (
                                        <option key={service.id} value={service.id}>{service.name}</option>
                                    ))}
                                </CustomSelect>
                            </Grid>
                            <Grid item xs={12} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                <Button type="submit" variant="contained" color="primary" disabled={loading}>
                                    {loading ? 'En cours...' : (isUpdate ? "Modifier la caisse" : "Ajouter la caisse")}
                                </Button>
                            </Grid>
                        </Grid>
                    </Form>
                </div>
            )}
        </Formik>
    );
}
