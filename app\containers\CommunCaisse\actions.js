// Actions for Commun Caisse
export const LOAD_CAISSES = 'app/CommunCaisse/LOAD_CAISSES';
export const LOAD_CAISSES_SUCCESS = 'app/CommunCaisse/LOAD_CAISSES_SUCCESS';
export const LOAD_CAISSES_ERROR = 'app/CommunCaisse/LOAD_CAISSES_ERROR';
export const ADD_CAISSE_REQUEST = 'app/CommunCaisse/ADD_CAISSE_REQUEST';
export const UPDATE_CAISSE_REQUEST = 'app/CommunCaisse/UPDATE_CAISSE_REQUEST';
export const DELETE_CAISSE = 'app/CommunCaisse/DELETE_CAISSE';
export const DELETE_CAISSE_SUCCESS = 'app/CommunCaisse/DELETE_CAISSE_SUCCESS';
export const RESET_ERROR = 'app/CommunCaisse/RESET_ERROR';

export function loadCaisses(page = 0) {
  return { type: LOAD_CAISSES, page };
}
export function loadCaissesSuccess(data) {
  return { type: LOAD_CAISSES_SUCCESS, data };
}
export function loadCaissesError(error) {
  return { type: LOAD_CAISSES_ERROR, error };
}
export function addCaisseRequest(payload) {
  return { type: ADD_CAISSE_REQUEST, payload };
}
export function updateCaisseRequest(payload) {
  return { type: UPDATE_CAISSE_REQUEST, payload };
}
export function deleteCaisse(id) {
  return { type: DELETE_CAISSE, id };
}
export function deleteCaisseSuccess(id) {
  return { type: DELETE_CAISSE_SUCCESS, id };
}
export function resetError() {
  return { type: RESET_ERROR };
}
