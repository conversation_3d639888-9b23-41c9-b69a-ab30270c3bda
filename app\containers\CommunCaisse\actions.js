// Actions for <PERSON><PERSON><PERSON> Caisse
import {
  LOAD_CAISSES,
  LOAD_CAISSES_SUCCESS,
  LOAD_CAISSES_ERROR,
  ADD_CAISSE_REQUEST,
  ADD_CAISSE_SUCCESS,
  ADD_CAISSE_ERROR,
  UPDATE_CAISSE_REQUEST,
  DELETE_CAISSE,
  DELETE_CAISSE_SUCCESS,
  LOAD_CAISSE_BY_ID,
  LOAD_CAISSE_BY_ID_SUCCESS,
  LOAD_CAISSE_BY_ID_ERROR,
  RESET_ERROR,
} from './constants';

export function loadCaisses(page = 0, filters = {}) {
  return { type: LOAD_CAISSES, page, filters };
}
export function loadCaissesSuccess(data) {
  return { type: LOAD_CAISSES_SUCCESS, data };
}
export function loadCaissesError(error) {
  return { type: LOAD_CAISSES_ERROR, error };
}
export function addCaisseRequest(payload) {
  return { type: ADD_CAISSE_REQUEST, payload };
}
export function addCaisseSuccess(data) {
  return { type: ADD_CAISSE_SUCCESS, data };
}
export function addCaisseError(error) {
  return { type: ADD_CAISSE_ERROR, error };
}
export function updateCaisseRequest(payload) {
  return { type: UPDATE_CAISSE_REQUEST, payload };
}
export function deleteCaisse(id) {
  return { type: DELETE_CAISSE, id };
}
export function deleteCaisseSuccess(id) {
  return { type: DELETE_CAISSE_SUCCESS, id };
}
export function loadCaisseById(id) {
  return { type: LOAD_CAISSE_BY_ID, id };
}
export function loadCaisseByIdSuccess(data) {
  return { type: LOAD_CAISSE_BY_ID_SUCCESS, data };
}
export function loadCaisseByIdError(error) {
  return { type: LOAD_CAISSE_BY_ID_ERROR, error };
}
export function resetError() {
  return { type: RESET_ERROR };
}
