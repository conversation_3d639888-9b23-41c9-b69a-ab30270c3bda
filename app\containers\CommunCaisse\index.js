import React, { useEffect, useState } from 'react';
import listStyles from 'Css/list.css';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import CustomPagination from 'components/Common/CustomPagination';
import Modal2 from 'components/Common/Modal2';
import btnStyles from 'Css/button.css';
import { createStructuredSelector } from 'reselect';
import { useHistory } from 'react-router-dom';
import DataTable from 'components/Common/DataTable';
import { Alert } from 'react-bootstrap';
import { makeSelectCaisses, makeSelectLoading, makeSelectDeleteSuccess, makeSelectError, makeSelectSuccess } from './selectors';
import communCaisseSaga from './saga';
import communCaisseReducer from './reducer';
import { loadCaisses, deleteCaisse, resetError } from './actions';
import CommunCaisseForm from './CommunCaisseForm';
import { DELETE_ICON, EDIT_ICON } from 'components/Common/ListIcons/ListIcons';
import { COMMUN_CAISSE_LIST_KEY } from './constants';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import ExportIconSvg from 'images/icons/ExportIconSvg';

const key = COMMUN_CAISSE_LIST_KEY;

const stateSelector = createStructuredSelector({
    caisses: makeSelectCaisses,
    loading: makeSelectLoading,
    error: makeSelectError,
    deleteSuccess: makeSelectDeleteSuccess,
    success: makeSelectSuccess,
});

export default function CommunCaisseList(props) {
    useInjectReducer({ key, reducer: communCaisseReducer });
    useInjectSaga({ key, saga: communCaisseSaga });

    const {
        caisses,
        loading,
        error,
        deleteSuccess,
        success,
    } = useSelector(stateSelector);
    const dispatch = useDispatch();
    const history = useHistory();
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [caisseToDelete, setCaisseToDelete] = useState('');
    const [errorMessages, setErrorMessages] = useState('');
    const [activePage, setActivePage] = useState(1);
    const [itemsCountPerPage, setItemsCountPerPage] = useState(10);
    const [totalItemsCount, setTotalItemsCount] = useState(0);
    const [filterValues, setFilterValues] = useState({
        donor: '',
        date_entre: '',
        date_sortie: '',
        status: '',
        amount: '',
    });

    // Filter configs
    const principalInputsConfig = [
        {
            field: 'donor',
            type: 'text',
            placeholder: 'Nom du donateur',
            label: 'Nom du Donateur',
        },
    ];
    const additionalInputsConfig = [
        {
            field: 'date_entre',
            type: 'date',
            placeholder: 'Date Entrée',
        },
        {
            field: 'date_sortie',
            type: 'date',
            placeholder: 'Date Sortie',
        },
        {
            field: 'status',
            type: 'select',
            placeholder: 'Statut',
            options: [
                { value: 'entree', label: 'Entrée' },
                { value: 'sortie', label: 'Sortie' },
            ]

        }, 
    ];

    // Data mapping for DataTable
    const rows = Array.isArray(caisses.content)
        ? caisses.content.map(caisse => ({
            id: caisse.id,
            donor: caisse.donor,
            amount: caisse.amount,
            date_entre: caisse.date_entre,
            date_sortie: caisse.date_sortie,
            service: caisse.service,
            status: caisse.status,
        }))
        : [];

    // Table columns
    const handleAdd = () => {
        history.push('/commun-caisse/add');
    };
    const handleEdit = (row) => {
        history.push(`/commun-caisse/edit/${row.id}`);
    };
    const columns = [
        { field: 'donor', headerName: 'Donor', flex: 1, headerAlign: 'center', align: 'center' },
        { field: 'amount', headerName: 'Amount', flex: 1, headerAlign: 'center', align: 'center' },
        { field: 'date_entre', headerName: 'Date Entrée', flex: 1, headerAlign: 'center', align: 'center' },
        { field: 'date_sortie', headerName: 'Date Sortie', flex: 1, headerAlign: 'center', align: 'center' },
        { field: 'service', headerName: 'Service', flex: 1, headerAlign: 'center', align: 'center' },
        { field: 'status', headerName: 'Status', flex: 1, headerAlign: 'center', align: 'center' },
        {
            field: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <>
                    <span onClick={() => handleEdit(params.row)}>{EDIT_ICON}</span>
                    <span onClick={() => { setCaisseToDelete(params.row.id); setShowDeleteModal(true); }}>{DELETE_ICON}</span>
                </>
            ),
        },
    ];

    // Filter apply handler
    const handleFilterApply = filters => {
        setFilterValues(filters);
        dispatch(loadCaisses(0, filters));
    };
    const handleResetFilterComplete = () => {
        setFilterValues({ donor: '', date_entre: '', date_sortie: '', status: '', amount: '' });
        dispatch(loadCaisses(0, {}));
    };

    // Pagination
    const handlePageChange = pageNumber => {
        setActivePage(pageNumber);
        dispatch(loadCaisses(pageNumber - 1, filterValues));
    };


    useEffect(() => {
        dispatch(loadCaisses(0, filterValues));
    }, [dispatch]);

    useEffect(() => {
        if (caisses) {
            setActivePage((caisses.number || 0) + 1);
            setItemsCountPerPage(caisses.pageSize || 10);
            setTotalItemsCount(caisses.totalElements || 0);
        }
    }, [caisses]);

    useEffect(() => {
        if (success) {
            setErrorMessages('La caisse emprunt est ajoutée avec succès !');
            dispatch(loadCaisses(0, filterValues));
            setTimeout(() => {
                setErrorMessages('');
                dispatch(resetError());
            }, 3000);
        }
    }, [success, dispatch, filterValues]);

    useEffect(() => {
        if (deleteSuccess) {
            setErrorMessages('La caisse a été supprimée avec succès !');
            dispatch(loadCaisses(0, filterValues));
            setTimeout(() => {
                setErrorMessages('');
                dispatch(resetError());
            }, 3000);
        }
    }, [deleteSuccess, dispatch, filterValues]);

    useEffect(() => {
        if (error) {
            const errorMessage = error.response?.data?.detailedMessage || error.message || 'Une erreur est survenue';
            setErrorMessages(errorMessage);
            setTimeout(() => setErrorMessages(''), 4000);
        }
    }, [error]);

    const handleDelete = (id) => {
        dispatch(deleteCaisse(id));
        setShowDeleteModal(false);
    };

    return (
        <div>
            {/* Alerts */}
            {errorMessages && (
                <Alert
                    variant={errorMessages.includes('succès') ? "success" : "danger"}
                    onClose={() => setErrorMessages('')}
                    dismissible
                >
                    {errorMessages}
                </Alert>
            )}
            <div className={listStyles.backgroundStyle}>
                {/* Filter Bar */}
                <GenericFilter
                    principalInputsConfig={principalInputsConfig}
                    additionalInputsConfig={additionalInputsConfig}
                    onApplyFilter={handleFilterApply}
                    setFilterValues={setFilterValues}
                    onResetFilterComplete={handleResetFilterComplete}
                    data={rows}
                />
                {/* Header with Add/Export */}
                <div className={listStyles.head} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <h4></h4>
                    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}>
                        <button className={`export-btn ${btnStyles.iconButton}`} disabled={rows.length === 0}>
                            <ExportIconSvg />
                        </button>
                        <button className={btnStyles.addBtnProfile} onClick={handleAdd} disabled={loading}>
                            {loading ? 'Chargement...' : 'Ajouter une caisse'}
                        </button>
                    </div>
                </div>
                {/* Table */}
                <div className="sub-container">
                    <div className="table-container">
                        {loading ? (
                            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                                <div className="spinner-border text-primary" role="status"></div>
                            </div>
                        ) : (
                            <>
                                <DataTable
                                    rows={rows}
                                    columns={columns}
                                    fileName={`Liste des Commun Caisses, ${new Date().toLocaleString()}`}
                                />
                                {caisses.totalPages > 0 && (
                                    <div className="justify-content-center mt-3">
                                        <CustomPagination
                                            totalElements={totalItemsCount}
                                            totalCount={caisses.totalPages}
                                            pageSize={itemsCountPerPage}
                                            currentPage={activePage}
                                            onPageChange={handlePageChange}
                                        />
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
            {/* Add/Edit Modal removed */}
            {/* Only keep Delete Modal */}
            <Modal2
                centered
                title="Confirmation de suppression"
                show={showDeleteModal}
                handleClose={() => setShowDeleteModal(false)}
            >
                <div className="p-3">
                    <p className="mb-4">Êtes-vous sûr de vouloir supprimer cette caisse ? Cette action est irréversible.</p>
                    <div className="d-flex justify-content-end gap-2">
                        <button type="button" className={btnStyles.cancelBtn} onClick={() => setShowDeleteModal(false)}>Annuler</button>
                        <button type="button" className={btnStyles.deleteBtn} onClick={() => handleDelete(caisseToDelete)}>Supprimer</button>
                    </div>
                </div>
            </Modal2>
        </div>
    );
}
