import {
  LOAD_CAISSES,
  LOAD_CAISSES_SUCCESS,
  LOAD_CAISSES_ERROR,
  ADD_CAISSE_REQUEST,
  UPDATE_CAISSE_REQUEST,
  DELETE_CAISSE,
  DELETE_CAISSE_SUCCESS,
  RESET_ERROR,
} from './actions';

const initialState = {
  caisses: { content: [], totalPages: 0, totalElements: 0, pageSize: 10, number: 0 },
  loading: false,
  error: null,
  deleteSuccess: false,
  success: false,
};

export default function communCaisseReducer(state = initialState, action) {
  switch (action.type) {
    case LOAD_CAISSES:
      return { ...state, loading: true, error: null };
    case LOAD_CAISSES_SUCCESS:
      return { ...state, loading: false, caisses: action.data, success: true };
    case LOAD_CAISSES_ERROR:
      return { ...state, loading: false, error: action.error };
    case ADD_CAISSE_REQUEST:
      return { ...state, loading: true, error: null, success: false };
    case UPDATE_CAISSE_REQUEST:
      return { ...state, loading: true, error: null, success: false };
    case DELETE_CAISSE:
      return { ...state, loading: true, error: null, deleteSuccess: false };
    case DELETE_CAISSE_SUCCESS:
      return { ...state, loading: false, deleteSuccess: true };
    case RESET_ERROR:
      return { ...state, error: null, success: false, deleteSuccess: false };
    default:
      return state;
  }
}
