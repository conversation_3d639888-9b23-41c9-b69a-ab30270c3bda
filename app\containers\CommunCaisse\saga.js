import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  LOAD_CAISSES,
  ADD_CAISSE_REQUEST,
  UPDATE_CAISSE_REQUEST,
  DELETE_CAISSE,
} from './constants';
import {
  loadCaissesSuccess,
  loadCaissesError,
  deleteCaisseSuccess,
  addCaisseSuccess,
  addCaisseError,
} from './actions';

function* fetchCaisses(action) {
  try {
    const { page = 0, filters = {} } = action;
    let url = `/caisses-emprunt?page=${page}&size=10`;

    // Add filter parameters if they exist
    if (filters.searchByDonorName) {
      url += `&searchByDonorName=${encodeURIComponent(filters.searchByDonorName)}`;
    }
    if (filters.searchByStatus) {
      url += `&searchByStatus=${encodeURIComponent(filters.searchByStatus)}`;
    }
    if (filters.searchByDateEmprunt) {
      // Convert date to ISO datetime format for backend
      const dateEmprunt = new Date(filters.searchByDateEmprunt);
      dateEmprunt.setHours(0, 0, 0, 0); // Start of day
      url += `&searchByDateEmprunt=${dateEmprunt.toISOString()}`;
    }
    if (filters.searchByDateRemboursement) {
      // Convert date to ISO datetime format for backend
      const dateRemboursement = new Date(filters.searchByDateRemboursement);
      dateRemboursement.setHours(0, 0, 0, 0); // Start of day
      url += `&searchByDateRemboursement=${dateRemboursement.toISOString()}`;
    }

    const { data } = yield call(request.get, url);
    yield put(loadCaissesSuccess(data));
  } catch (error) {
    yield put(loadCaissesError(error));
  }
}

function* addCaisse(action) {
  const url = '/caisses-emprunt';
  try {
    // Transform the form data to match the required DTO structure
    const { donor, amount, dateEmprunt, service } = action.payload;

    // Transform date to local datetime (add current time to the date)
    const dateWithTime = new Date(dateEmprunt);
    const now = new Date();
    dateWithTime.setHours(now.getHours(), now.getMinutes(), now.getSeconds());

    const requestBody = {
      donorId: donor.id,
      serviceId: parseInt(service, 10),
      amount: parseFloat(amount),
      dateEmprunt: dateWithTime.toISOString()
    };

    const { data } = yield call(request.post, url, requestBody);
    yield put(addCaisseSuccess(data));
    // Reload the caisses list after successful add
    yield call(fetchCaisses, { page: 0, filters: {} });
  } catch (error) {
    yield put(addCaisseError(error));
  }
}

function* updateCaisse(action) {
  const url = `/caisses-emprunt/${action.payload.id}`;
  try {
    // Transform the form data to match the required DTO structure
    const { donor, amount, dateEmprunt, service } = action.payload;

    // Transform date to local datetime (add current time to the date)
    const dateWithTime = new Date(dateEmprunt);
    const now = new Date();
    dateWithTime.setHours(now.getHours(), now.getMinutes(), now.getSeconds());

    const requestBody = {
      donorId: donor.id,
      serviceId: parseInt(service, 10),
      amount: parseFloat(amount),
      dateEmprunt: dateWithTime.toISOString()
    };

    yield call(request.put, url, requestBody);
    // Reload the caisses list after successful update
    yield call(fetchCaisses, { page: 0, filters: {} });
  } catch (error) {
    yield put(addCaisseError(error));
  }
}

function* deleteCaisse(action) {
  const url = `/caisses-emprunt/${action.id}`;
  try {
    yield call(request.delete, url);
    yield put(deleteCaisseSuccess(action.id));
    // Reload the list after successful delete
    yield call(fetchCaisses, { page: 0, filters: {} });
  } catch (error) {
    yield put(loadCaissesError(error));
  }
}

export default function* communCaisseSaga() {
  yield takeLatest(LOAD_CAISSES, fetchCaisses);
  yield takeLatest(ADD_CAISSE_REQUEST, addCaisse);
  yield takeLatest(UPDATE_CAISSE_REQUEST, updateCaisse);
  yield takeLatest(DELETE_CAISSE, deleteCaisse);
}
