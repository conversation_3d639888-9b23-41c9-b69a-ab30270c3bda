import { call, put, takeLatest } from 'redux-saga/effects';
import {
  LOAD_CAISSES,
  ADD_CAISSE_REQUEST,
  UPDATE_CAISSE_REQUEST,
  DELETE_CAISSE,
  loadCaissesSuccess,
  loadCaissesError,
  deleteCaisseSuccess,
} from './actions';

// Dummy API calls (replace with real API)
function* fetchCaisses() {
  try {
    // Replace with real API call
    const data = { content: [], totalPages: 0, totalElements: 0, pageSize: 10, number: 0 };
    yield put(loadCaissesSuccess(data));
  } catch (error) {
    yield put(loadCaissesError(error));
  }
}

function* addCaisse(action) {
  // Implement API call
  yield call(fetchCaisses);
}

function* updateCaisse(action) {
  // Implement API call
  yield call(fetchCaisses);
}

function* deleteCaisse(action) {
  // Implement API call
  yield put(deleteCaisseSuccess(action.id));
  yield call(fetchCaisses);
}

export default function* communCaisseSaga() {
  yield takeLatest(LOAD_CAISSES, fetchCaisses);
  yield takeLatest(ADD_CAISSE_REQUEST, addCaisse);
  yield takeLatest(UPDATE_CAISSE_REQUEST, updateCaisse);
  yield takeLatest(DELETE_CAISSE, deleteCaisse);
}
