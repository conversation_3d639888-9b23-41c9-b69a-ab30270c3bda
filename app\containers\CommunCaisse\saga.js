import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  LOAD_CAISSES,
  ADD_CAISSE_REQUEST,
  UPDATE_CAISSE_REQUEST,
  DELETE_CAISSE,
} from './constants';
import {
  loadCaissesSuccess,
  loadCaissesError,
  deleteCaisseSuccess,
  addCaisseSuccess,
  addCaisseError,
} from './actions';

// Dummy API calls (replace with real API)
function* fetchCaisses() {
  try {
    // Replace with real API call
    const data = { content: [], totalPages: 0, totalElements: 0, pageSize: 10, number: 0 };
    yield put(loadCaissesSuccess(data));
  } catch (error) {
    yield put(loadCaissesError(error));
  }
}

function* addCaisse(action) {
  const url = '/caisses-emprunt';
  try {
    // Transform the form data to match the required DTO structure
    const { donor, amount, dateEmprunt, service } = action.payload;

    // Transform date to local datetime (add current time to the date)
    const dateWithTime = new Date(dateEmprunt);
    const now = new Date();
    dateWithTime.setHours(now.getHours(), now.getMinutes(), now.getSeconds());

    const requestBody = {
      donorId: donor.id,
      serviceId: parseInt(service, 10),
      amount: parseFloat(amount),
      dateEmprunt: dateWithTime.toISOString()
    };

    const { data } = yield call(request.post, url, requestBody);
    yield put(addCaisseSuccess(data));
    // Reload the caisses list after successful add
    yield call(fetchCaisses);
  } catch (error) {
    yield put(addCaisseError(error));
  }
}

function* updateCaisse(action) {
  // Implement API call
  yield call(fetchCaisses);
}

function* deleteCaisse(action) {
  // Implement API call
  yield put(deleteCaisseSuccess(action.id));
  yield call(fetchCaisses);
}

export default function* communCaisseSaga() {
  yield takeLatest(LOAD_CAISSES, fetchCaisses);
  yield takeLatest(ADD_CAISSE_REQUEST, addCaisse);
  yield takeLatest(UPDATE_CAISSE_REQUEST, updateCaisse);
  yield takeLatest(DELETE_CAISSE, deleteCaisse);
}
