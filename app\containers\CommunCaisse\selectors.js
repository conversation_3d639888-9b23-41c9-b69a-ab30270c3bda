import { createSelector } from 'reselect';

const selectCommunCaisseDomain = state => state.communCaisseList || {};

export const makeSelectCaisses = createSelector(
  selectCommunCaisseDomain,
  substate => substate.caisses || { content: [], totalPages: 0, totalElements: 0, pageSize: 10, number: 0 }
);

export const makeSelectLoading = createSelector(
  selectCommunCaisseDomain,
  substate => substate.loading || false
);

export const makeSelectDeleteSuccess = createSelector(
  selectCommunCaisseDomain,
  substate => substate.deleteSuccess || false
);

export const makeSelectError = createSelector(
  selectCommunCaisseDomain,
  substate => substate.error || null
);

export const makeSelectSuccess = createSelector(
  selectCommunCaisseDomain,
  substate => substate.success || false
);
